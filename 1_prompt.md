# Prompt for Developer: Fix Drag & Drop Sorting on Manage Sections Screen

### **Objective**

To debug and fix the drag-and-drop functionality on the "Manage Sections" screen. The goal is to ensure the interaction is smooth, the dragged item follows the user's finger accurately, and the drop-target animation correctly predicts the final position.

### **Context & Root Cause**

The current drag-and-drop sorting implementation on the "Manage Sections" screen is buggy. While the reordering function works, the user experience is poor.

**Identified Issues:**
1.  **Laggy Movement:** The section card being dragged does not keep up with the user's finger movement, making the interaction feel sluggish and disconnected.
2.  **Broken Drop Animation:** The visual indicator that shows where the card will be placed upon release (i.e., the space that opens up in the list) is broken. It does not appear in the correct location, making it impossible for the user to predict where the card will land.
3.  **Unpredictable Result:** Due to the broken animation, the final position of the dropped card often does not match the user's intent.

**Suspected Root Cause:** The problem likely resides in the UI layer's state handling during the drag gesture. The logic responsible for calculating the drag offset and determining the current drop target is either inefficient or incorrect, leading to performance issues (lag) and visual glitches (broken animation).

---

### **Part 1: Detailed Implementation Plan**

1.  **Optimize Drag Gesture Handling:**
    * Navigate to the composable responsible for rendering the list of section cards on the "Manage Sections" screen.
    * Review the drag gesture detector. Ensure that the state updates for the item's visual offset are lightweight and do not trigger expensive recompositions of the entire list during the drag. The goal is to have the card follow the finger's Y-position in real-time.

2.  **Correct the Drop Target Calculation:**
    * The most critical task is to fix the logic that determines which list item is currently being hovered over.
    * This logic must accurately calculate the boundaries of each item in the list and compare them against the dragged item's current position to decide where the empty "drop" space should be rendered.
    * When a hover target is identified, the list should animate smoothly to create a gap, providing clear feedback to the user.

3.  **Ensure Smooth Animations:**
    * The entire interaction should be fluid. This includes the initial "lift" animation when the user starts dragging and the final "drop" animation when they release the card.
    * Consult the `style.md` file for animation standards. The `Drag Feedback` micro-interaction specifies a `transform: scale(0.9)` and `opacity: 0.8` effect that should be applied to the item being dragged.

---

### **Part 2: Verification Steps**

1.  **Navigate** to the "Manage Sections" screen.
2.  **Press and Hold:** Long-press any section card. Verify that it lifts smoothly and the "dragging" visual state (e.g., scale and opacity change) is applied.
3.  **Drag Smoothly:** Move your finger up and down the list. The card **must** follow your finger precisely and without any noticeable lag or jitter.
4.  **Verify Drop Target:** As you drag the card over the vertical center of another card, verify that a space immediately and correctly opens up to indicate the new drop position. This animation must be predictable and reliable.
5.  **Drop the Card:** Release your finger. The card should animate smoothly into the new position, and the list's order should be correctly updated.
6.  **Confirm Persistence:** Navigate back to the previous screen and then return to the "Manage Sections" screen. The new custom order must be preserved.

---

## ⚙️ Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation. 
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.

---

By adhering to these principles and mandatory practices, we ensure clarity, precision, and high-quality development that scales well over time.